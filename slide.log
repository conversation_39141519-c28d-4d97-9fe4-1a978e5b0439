This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024) (preloaded format=xelatex 2024.11.28)  8 JUL 2025 21:05
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"d:/Downloads/USTC Beamer Theme_from_OVERLEAF/slide"
(d:/Downloads/USTC Beamer Theme_from_OVERLEAF/slide.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(d:/texlive/2024/texmf-dist/tex/latex/beamer/beamer.cls
Document Class: beamer 2024/01/06 v3.71 A class for typesetting presentations
(d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasemodes.sty (d:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count184
)
\beamer@tempbox=\box51
\beamer@tempcount=\count185
\c@beamerpauses=\count186
 (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasedecode.sty
\beamer@slideinframe=\count187
\beamer@minimum=\count188
\beamer@decode@box=\box52
)
\beamer@commentbox=\box53
\beamer@modecount=\count189
) (d:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)
\headdp=\dimen140
\footheight=\dimen141
\sidebarheight=\dimen142
\beamer@tempdim=\dimen143
\beamer@finalheight=\dimen144
\beamer@animht=\dimen145
\beamer@animdp=\dimen146
\beamer@animwd=\dimen147
\beamer@leftmargin=\dimen148
\beamer@rightmargin=\dimen149
\beamer@leftsidebar=\dimen150
\beamer@rightsidebar=\dimen151
\beamer@boxsize=\dimen152
\beamer@vboxoffset=\dimen153
\beamer@descdefault=\dimen154
\beamer@descriptionwidth=\dimen155
\beamer@lastskip=\skip48
\beamer@areabox=\box54
\beamer@animcurrent=\box55
\beamer@animshowbox=\box56
\beamer@sectionbox=\box57
\beamer@logobox=\box58
\beamer@linebox=\box59
\beamer@sectioncount=\count190
\beamer@subsubsectionmax=\count191
\beamer@subsectionmax=\count192
\beamer@sectionmax=\count193
\beamer@totalheads=\count194
\beamer@headcounter=\count195
\beamer@partstartpage=\count196
\beamer@sectionstartpage=\count197
\beamer@subsectionstartpage=\count198
\beamer@animationtempa=\count199
\beamer@animationtempb=\count266
\beamer@xpos=\count267
\beamer@ypos=\count268
\beamer@ypos@offset=\count269
\beamer@showpartnumber=\count270
\beamer@currentsubsection=\count271
\beamer@coveringdepth=\count272
\beamer@sectionadjust=\count273
\beamer@toclastsection=\count274
\beamer@tocsectionnumber=\count275
 (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseoptions.sty (d:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
))
\beamer@paperwidth=\skip49
\beamer@paperheight=\skip50
 (d:/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (d:/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count276
\Gm@cntv=\count277
\c@Gm@tempcnt=\count278
\Gm@bindingoffset=\dimen156
\Gm@wd@mp=\dimen157
\Gm@odd@mp=\dimen158
\Gm@even@mp=\dimen159
\Gm@layoutwidth=\dimen160
\Gm@layoutheight=\dimen161
\Gm@layouthoffset=\dimen162
\Gm@layoutvoffset=\dimen163
\Gm@dimlist=\toks18
) (d:/texlive/2024/texmf-dist/tex/latex/pgf/math/pgfmath.sty (d:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (d:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks19
\pgfutil@tempdima=\dimen164
\pgfutil@tempdimb=\dimen165
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box60
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (d:/texlive/2024/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
)) (d:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (d:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks20
\pgfkeys@temptoks=\toks21
 (d:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks22
))) (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex
\pgf@x=\dimen166
\pgf@xa=\dimen167
\pgf@xb=\dimen168
\pgf@xc=\dimen169
\pgf@y=\dimen170
\pgf@ya=\dimen171
\pgf@yb=\dimen172
\pgf@yc=\dimen173
\c@pgf@counta=\count279
\c@pgf@countb=\count280
\c@pgf@countc=\count281
\c@pgf@countd=\count282
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen174
\pgfmath@count=\count283
\pgfmath@box=\box61
\pgfmath@toks=\toks23
\pgfmath@stack@operand=\toks24
\pgfmath@stack@operation=\toks25
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count284
))) (d:/texlive/2024/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
) (d:/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (d:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (d:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
 (d:/texlive/2024/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen175
\Gin@req@width=\dimen176
) (d:/texlive/2024/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (d:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
\pgf@x=\dimen177
\pgf@y=\dimen178
\pgf@xa=\dimen179
\pgf@ya=\dimen180
\pgf@xb=\dimen181
\pgf@yb=\dimen182
\pgf@xc=\dimen183
\pgf@yc=\dimen184
\pgf@xd=\dimen185
\pgf@yd=\dimen186
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count285
\c@pgf@countb=\count286
\c@pgf@countc=\count287
\c@pgf@countd=\count288
\t@pgf@toka=\toks26
\t@pgf@tokb=\toks27
\t@pgf@tokc=\toks28
\pgf@sys@id@count=\count289
 (d:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-xetex.def
 (d:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-xetex.def
File: pgfsys-xetex.def 2023-01-15 v3.1.10 (3.1.10)
 (d:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2023-01-15 v3.1.10 (3.1.10)
 (d:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)
\pgfsys@objnum=\count290
))) (d:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count291
\pgfsyssoftpath@bigbuffer@items=\count292
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (d:/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (d:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen187
\pgf@picmaxx=\dimen188
\pgf@picminy=\dimen189
\pgf@picmaxy=\dimen190
\pgf@pathminx=\dimen191
\pgf@pathmaxx=\dimen192
\pgf@pathminy=\dimen193
\pgf@pathmaxy=\dimen194
\pgf@xx=\dimen195
\pgf@xy=\dimen196
\pgf@yx=\dimen197
\pgf@yy=\dimen198
\pgf@zx=\dimen199
\pgf@zy=\dimen256
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen257
\pgf@path@lasty=\dimen258
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen259
\pgf@shorten@start@additional=\dimen260
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box62
\pgf@hbox=\box63
\pgf@layerbox@main=\box64
\pgf@picture@serial@count=\count293
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen261
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen262
\pgf@pt@y=\dimen263
\pgf@pt@temp=\dimen264
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen265
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen266
\pgf@sys@shading@range@num=\count294
\pgf@shadingcount=\count295
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box65
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (d:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (d:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/xxcolor.sty
Package: xxcolor 2003/10/24 ver 0.1
\XC@nummixins=\count296
\XC@countmixins=\count297
) (d:/texlive/2024/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
) (d:/texlive/2024/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (d:/texlive/2024/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (d:/texlive/2024/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (d:/texlive/2024/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (d:/texlive/2024/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (d:/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count298
)
\@linkdim=\dimen267
\Hy@linkcounter=\count299
\Hy@pagecounter=\count300
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count301
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `bookmarks' set `true' on input line 4062.
Package hyperref Info: Option `bookmarksopen' set `true' on input line 4062.
Package hyperref Info: Option `implicit' set `false' on input line 4062.
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing OFF on input line 4199.
Package hyperref Info: Implicit mode OFF; no redefinition of LaTeX internals.
Package hyperref Info: Bookmarks ON on input line 4446.
\c@Hy@tempcnt=\count302
 (d:/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip16
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen268
 (d:/texlive/2024/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count303
\Field@Width=\dimen269
\Fld@charsize=\dimen270
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing OFF on input line 6078.
Package hyperref Info: Link coloring OFF on input line 6083.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.
\Hy@abspage=\count304


Package hyperref Message: Stopped early.

)
Package hyperref Info: Driver (autodetected): hxetex.
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2024-01-20 v7.01h Hyperref driver for XeTeX
 (d:/texlive/2024/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\pdfm@box=\box66
\c@Hy@AnnotLevel=\count305
\HyField@AnnotCount=\count306
\Fld@listcount=\count307
\c@bookmark@seq@number=\count308
 (d:/texlive/2024/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (d:/texlive/2024/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (d:/texlive/2024/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaserequires.sty (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasecompatibility.sty) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasefont.sty (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks29
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (d:/texlive/2024/texmf-dist/tex/latex/sansmathaccent/sansmathaccent.sty
Package: sansmathaccent 2020/01/31
 (d:/texlive/2024/texmf-dist/tex/latex/koma-script/scrlfile.sty
Package: scrlfile 2023/07/07 v3.41 KOMA-Script package (file load hooks)
 (d:/texlive/2024/texmf-dist/tex/latex/koma-script/scrlfile-hook.sty
Package: scrlfile-hook 2023/07/07 v3.41 KOMA-Script package (using LaTeX hooks)
 (d:/texlive/2024/texmf-dist/tex/latex/koma-script/scrlogo.sty
Package: scrlogo 2023/07/07 v3.41 KOMA-Script package (logo)
))))) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasetranslator.sty (d:/texlive/2024/texmf-dist/tex/latex/translator/translator.sty
Package: translator 2021-05-31 v1.12d Easy translation of strings in LaTeX
)) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasemisc.sty) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasetwoscreens.sty) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseoverlay.sty
\beamer@argscount=\count309
\beamer@lastskipcover=\skip51
\beamer@trivlistdepth=\count310
) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasetitle.sty) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasesection.sty
\c@lecture=\count311
\c@part=\count312
\c@section=\count313
\c@subsection=\count314
\c@subsubsection=\count315
) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseframe.sty
\beamer@framebox=\box67
\beamer@frametitlebox=\box68
\beamer@zoombox=\box69
\beamer@zoomcount=\count316
\beamer@zoomframecount=\count317
\beamer@frametextheight=\dimen271
\c@subsectionslide=\count318
\beamer@frametopskip=\skip52
\beamer@framebottomskip=\skip53
\beamer@frametopskipautobreak=\skip54
\beamer@framebottomskipautobreak=\skip55
\beamer@envbody=\toks30
\framewidth=\dimen272
\c@framenumber=\count319
) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseverbatim.sty
\beamer@verbatimfileout=\write4
) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseframesize.sty
\beamer@splitbox=\box70
\beamer@autobreakcount=\count320
\beamer@autobreaklastheight=\dimen273
\beamer@frametitletoks=\toks31
\beamer@framesubtitletoks=\toks32
) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseframecomponents.sty
\beamer@footins=\box71
) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasecolor.sty) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasenotes.sty
\beamer@frameboxcopy=\box72
) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasetoc.sty) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasetemplates.sty
\beamer@sbttoks=\toks33
 (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseauxtemplates.sty (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseboxes.sty
\bmb@box=\box73
\bmb@colorbox=\box74
\bmb@boxwidth=\dimen274
\bmb@boxheight=\dimen275
\bmb@prevheight=\dimen276
\bmb@temp=\dimen277
\bmb@dima=\dimen278
\bmb@dimb=\dimen279
\bmb@prevheight=\dimen280
)
\beamer@blockheadheight=\dimen281
)) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaselocalstructure.sty (d:/texlive/2024/texmf-dist/tex/latex/tools/enumerate.sty
Package: enumerate 2023/07/04 v3.00 enumerate extensions (DPC)
\@enLab=\toks34
)
\beamer@bibiconwidth=\skip56
\c@figure=\count321
\c@table=\count322
\abovecaptionskip=\skip57
\belowcaptionskip=\skip58
) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasenavigation.sty (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasenavigationsymbols.tex)
\beamer@section@min@dim=\dimen282
) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasetheorems.sty (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip59

For additional information on amsmath, use the `?' option.
(d:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks35
\ex@=\dimen283
)) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen284
) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count323
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count324
\leftroot@=\count325
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count326
\DOTSCASE@=\count327
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box75
\strutbox@=\box76
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen285
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count328
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count329
\dotsspace@=\muskip17
\c@parentequation=\count330
\dspbrk@lvl=\count331
\tag@help=\toks36
\row@=\count332
\column@=\count333
\maxfields@=\count334
\andhelp@=\toks37
\eqnshift@=\dimen286
\alignsep@=\dimen287
\tagshift@=\dimen288
\tagwidth@=\dimen289
\totwidth@=\dimen290
\lineht@=\dimen291
\@envbody=\toks38
\multlinegap=\skip60
\multlinetaggap=\skip61
\mathdisplay@stack=\toks39
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (d:/texlive/2024/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks40
\thm@bodyfont=\toks41
\thm@headfont=\toks42
\thm@notefont=\toks43
\thm@headpunct=\toks44
\thm@preskip=\skip62
\thm@postskip=\skip63
\thm@headsep=\skip64
\dth@everypar=\toks45
)
\c@theorem=\count335
) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasethemes.sty)) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerthemedefault.sty (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerfontthemedefault.sty) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamercolorthemedefault.sty) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerinnerthemedefault.sty
\beamer@dima=\dimen292
\beamer@dimb=\dimen293
) (d:/texlive/2024/texmf-dist/tex/latex/beamer/beamerouterthemedefault.sty))) (d:/texlive/2024/texmf-dist/tex/latex/ctex/ctex.sty (d:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 
 (d:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count336
\l__pdf_internal_box=\box77
\g__pdf_backend_object_int=\count337
\g__pdf_backend_annotation_int=\count338
\g__pdf_backend_link_int=\count339
))
Package: ctex 2022/07/14 v2.5.10 Chinese adapter in LaTeX (CTEX)
 (d:/texlive/2024/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (d:/texlive/2024/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
) (d:/texlive/2024/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count340
\l__ctex_tmp_box=\box78
\l__ctex_tmp_dim=\dimen294
\g__ctex_section_depth_int=\count341
\g__ctex_font_size_int=\count342
 (d:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
) (d:/texlive/2024/texmf-dist/tex/latex/ctex/engine/ctex-engine-xetex.def
File: ctex-engine-xetex.def 2022/07/14 v2.5.10 XeLaTeX adapter (CTEX)
 (d:/texlive/2024/texmf-dist/tex/xelatex/xecjk/xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate.sty (d:/texlive/2024/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate-2023-10-10.sty
Package: xtemplate 2023-10-10 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen295
\l__xtemplate_tmp_int=\count343
\l__xtemplate_tmp_muskip=\muskip18
\l__xtemplate_tmp_skip=\skip65
))
\l__xeCJK_tmp_int=\count344
\l__xeCJK_tmp_box=\box79
\l__xeCJK_tmp_dim=\dimen296
\l__xeCJK_tmp_skip=\skip66
\g__xeCJK_space_factor_int=\count345
\l__xeCJK_begin_int=\count346
\l__xeCJK_end_int=\count347
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip67
\c__xeCJK_none_node=\count348
\g__xeCJK_node_int=\count349
\c__xeCJK_CJK_node_dim=\dimen297
\c__xeCJK_CJK-space_node_dim=\dimen298
\c__xeCJK_default_node_dim=\dimen299
\c__xeCJK_CJK-widow_node_dim=\dimen300
\c__xeCJK_normalspace_node_dim=\dimen301
\c__xeCJK_default-space_node_skip=\skip68
\l__xeCJK_ccglue_skip=\skip69
\l__xeCJK_ecglue_skip=\skip70
\l__xeCJK_punct_kern_skip=\skip71
\l__xeCJK_indent_box=\box80
\l__xeCJK_last_penalty_int=\count350
\l__xeCJK_last_bound_dim=\dimen302
\l__xeCJK_last_kern_dim=\dimen303
\l__xeCJK_widow_penalty_int=\count351

Package xtemplate Info: Declaring object type 'xeCJK/punctuation' taking 0
(xtemplate)             argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen304
\l__xeCJK_mixed_punct_width_dim=\dimen305
\l__xeCJK_middle_punct_width_dim=\dimen306
\l__xeCJK_fixed_margin_width_dim=\dimen307
\l__xeCJK_mixed_margin_width_dim=\dimen308
\l__xeCJK_middle_margin_width_dim=\dimen309
\l__xeCJK_bound_punct_width_dim=\dimen310
\l__xeCJK_bound_margin_width_dim=\dimen311
\l__xeCJK_margin_minimum_dim=\dimen312
\l__xeCJK_kerning_total_width_dim=\dimen313
\l__xeCJK_same_align_margin_dim=\dimen314
\l__xeCJK_different_align_margin_dim=\dimen315
\l__xeCJK_kerning_margin_width_dim=\dimen316
\l__xeCJK_kerning_margin_minimum_dim=\dimen317
\l__xeCJK_bound_dim=\dimen318
\l__xeCJK_reverse_bound_dim=\dimen319
\l__xeCJK_margin_dim=\dimen320
\l__xeCJK_minimum_bound_dim=\dimen321
\l__xeCJK_kerning_margin_dim=\dimen322
\g__xeCJK_family_int=\count352
\l__xeCJK_fam_int=\count353
\g__xeCJK_fam_allocation_int=\count354
\l__xeCJK_verb_case_int=\count355
\l__xeCJK_verb_exspace_skip=\skip72
 (d:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.sty (d:/texlive/2024/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-02-18 L3 Experimental document command parser
)
Package: fontspec 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count356
\l__fontspec_language_int=\count357
\l__fontspec_strnum_int=\count358
\l__fontspec_tmp_int=\count359
\l__fontspec_tmpa_int=\count360
\l__fontspec_tmpb_int=\count361
\l__fontspec_tmpc_int=\count362
\l__fontspec_em_int=\count363
\l__fontspec_emdef_int=\count364
\l__fontspec_strong_int=\count365
\l__fontspec_strongdef_int=\count366
\l__fontspec_tmpa_dim=\dimen323
\l__fontspec_tmpb_dim=\dimen324
\l__fontspec_tmpc_dim=\dimen325
 (d:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (d:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (d:/texlive/2024/texmf-dist/tex/xelatex/xecjk/xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))
\ccwd=\dimen326
\l__ctex_ccglue_skip=\skip73
)
\l__ctex_ziju_dim=\dimen327
 (d:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count367
\l__zhnum_tmp_int=\count368
 (d:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
)) (d:/texlive/2024/texmf-dist/tex/latex/ctex/scheme/ctex-scheme-chinese.def
File: ctex-scheme-chinese.def 2022/07/14 v2.5.10 Chinese scheme for generic (CTEX)
 (d:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
)) (d:/texlive/2024/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTEX)

Package fontspec Info: Could not resolve font "KaiTi/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimHei/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimSun/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Font family 'SimSun(0)' created for font 'SimSun' with
(fontspec)             options
(fontspec)             [Script={CJK},BoldFont={SimHei},ItalicFont={KaiTi}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimSun/OT:script=hani;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"SimHei/OT:script=hani;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"KaiTi/OT:script=hani;language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.: 

)) (d:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
) (d:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
LaTeX Font Info:    Trying to load font information for T1+lmss on input line 112.
 (d:/texlive/2024/texmf-dist/tex/latex/lm/t1lmss.fd
File: t1lmss.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)) (d:/texlive/2024/texmf-dist/tex/latex/base/latexsym.sty
Package: latexsym 1998/08/17 v2.2e Standard LaTeX package (lasy symbols)
\symlasy=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `lasy' in version `bold'
(Font)                  U/lasy/m/n --> U/lasy/b/n on input line 52.
) (d:/texlive/2024/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2023/03/30 v1.9f multicolumn formatting (FMi)
\c@tracingmulticols=\count369
\mult@box=\box81
\multicol@leftmargin=\dimen328
\c@unbalance=\count370
\c@collectmore=\count371
\doublecol@number=\count372
\multicoltolerance=\count373
\multicolpretolerance=\count374
\full@width=\dimen329
\page@free=\dimen330
\premulticols=\dimen331
\postmulticols=\dimen332
\multicolsep=\skip74
\multicolbaselineskip=\skip75
\partial@page=\box82
\last@line=\box83
\mc@boxedresult=\box84
\maxbalancingoverflow=\dimen333
\mult@rightbox=\box85
\mult@grightbox=\box86
\mult@firstbox=\box87
\mult@gfirstbox=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\@tempa=\box99
\@tempa=\box100
\@tempa=\box101
\@tempa=\box102
\@tempa=\box103
\@tempa=\box104
\@tempa=\box105
\@tempa=\box106
\@tempa=\box107
\@tempa=\box108
\@tempa=\box109
\@tempa=\box110
\@tempa=\box111
\@tempa=\box112
\@tempa=\box113
\@tempa=\box114
\@tempa=\box115
\@tempa=\box116
\@tempa=\box117
\@tempa=\box118
\@tempa=\box119
\@tempa=\box120
\@tempa=\box121
\@tempa=\box122
\@tempa=\box123
\@tempa=\box124
\c@minrows=\count375
\c@columnbadness=\count376
\c@finalcolumnbadness=\count377
\last@try=\dimen334
\multicolovershoot=\dimen335
\multicolundershoot=\dimen336
\mult@nat@firstbox=\box125
\colbreak@box=\box126
\mc@col@check@num=\count378
) (d:/texlive/2024/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen337
\lightrulewidth=\dimen338
\cmidrulewidth=\dimen339
\belowrulesep=\dimen340
\belowbottomsep=\dimen341
\aboverulesep=\dimen342
\abovetopsep=\dimen343
\cmidrulesep=\dimen344
\cmidrulekern=\dimen345
\defaultaddspace=\dimen346
\@cmidla=\count379
\@cmidlb=\count380
\@aboverulesep=\dimen347
\@belowrulesep=\dimen348
\@thisruleclass=\count381
\@lastruleclass=\count382
\@thisrulewidth=\dimen349
) (d:/texlive/2024/texmf-dist/tex/latex/fundus-calligra/calligra.sty
Package: calligra 2012/04/10 v1.9 LaTeX package calligra
) (d:/texlive/2024/texmf-dist/tex/latex/pstricks/pstricks.sty
Package: pstricks 2024/02/02 v0.75 LaTeX wrapper for `PSTricks' (RN,HV)
 (d:/texlive/2024/texmf-dist/tex/generic/xkeyval/pst-xkey.tex
File: pst-xkey.tex 2005/11/25 v1.6 PSTricks specialization of xkeyval (HA)
 (d:/texlive/2024/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (d:/texlive/2024/texmf-dist/tex/generic/xkeyval/xkeyval.tex (d:/texlive/2024/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks46
\XKV@tempa@toks=\toks47
)
\XKV@depth=\count383
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))) (d:/texlive/2024/texmf-dist/tex/generic/pstricks/pstricks.tex (d:/texlive/2024/texmf-dist/tex/generic/pstricks/pst-fp.tex `pst-fp' v0.06, 2020/11/20 (hv)
\pstFP@xs=\count384
\pstFP@xia=\count385
\pstFP@xib=\count386
\pstFP@xfa=\count387
\pstFP@xfb=\count388
\pstFP@rega=\count389
\pstFP@regb=\count390
\pstFP@regs=\count391
\pstFP@times=\count392
)
\psLoopIndex=\count393

`PSTricks' v3.19c  <2024/02/02> (tvz,hv)
\pst@dima=\dimen350
\pst@dimb=\dimen351
\pst@dimc=\dimen352
\pst@dimd=\dimen353
\pst@dimg=\dimen354
\pst@dimh=\dimen355
\pst@dimm=\dimen356
\pst@dimn=\dimen357
\pst@dimo=\dimen358
\pst@dimp=\dimen359
\pst@hbox=\box127
\pst@ibox=\box128
\pst@boxg=\box129
\pst@cnta=\count394
\pst@cntb=\count395
\pst@cntc=\count396
\pst@cntd=\count397
\pst@cntg=\count398
\pst@cnth=\count399
\pst@cntm=\count400
\pst@cntn=\count401
\pst@cnto=\count402
\pst@cntp=\count403
\@zero=\count404
\pst@toks=\toks48
--- We are running latex or xelatex ---
(d:/texlive/2024/texmf-dist/tex/xelatex/xetex-pstricks/pstricks.con (d:/texlive/2024/texmf-dist/tex/generic/pstricks/config/xdvipdfmx.cfg)) (d:/texlive/2024/texmf-dist/tex/generic/pstricks/pstricks-color.tex)
\psunit=\dimen360
\psxunit=\dimen361
\psyunit=\dimen362
\pst@C@@rType=\count405
\pslinewidth=\dimen363
\psk@startLW=\dimen364
\psk@endLW=\dimen365
 (d:/texlive/2024/texmf-dist/tex/generic/pstricks/pstricks-arrows.tex
\pshooklength=\dimen366
\pshookwidth=\dimen367
)
\pst@customdefs=\toks49
 (d:/texlive/2024/texmf-dist/tex/generic/pstricks/pstricks-dots.tex)
\pslinearc=\dimen368
\pst@symbolStep=\dimen369
\pst@symbolWidth=\dimen370
\pst@symbolLinewidth=\dimen371
\everypsbox=\toks50
\psframesep=\dimen372
\pslabelsep=\dimen373
\sh@wgridXunit=\dimen374
\sh@wgridYunit=\dimen375
\pst@shift=\dimen376
\ps@imagectr=\count406
 (d:/texlive/2024/texmf-dist/tex/xelatex/xetex-pstricks/pstricks.con (d:/texlive/2024/texmf-dist/tex/generic/pstricks/config/xdvipdfmx.cfg
Using PSTricks configuration for LaTeX+dvipdfmx
)))
File: pstricks.tex 2024/02/02 v3.19c `PSTricks' (tvz,hv)
 (d:/texlive/2024/texmf-dist/tex/generic/pstricks/pst-fp.tex `pst-fp' v0.06, 2020/11/20 (hv))
File: pst-fp.tex 2020/11/20 v0.06 `PST-fp' (hv)

>>> Loading XeTeX special macros
(d:/texlive/2024/texmf-dist/tex/generic/pstricks/pstricks-xetex.def)) (d:/texlive/2024/texmf-dist/tex/latex/stackengine/stackengine.sty
Package: stackengine 2021/07/22 v4.11\ Stacking text and objects in convenient ways
 (d:/texlive/2024/texmf-dist/tex/generic/listofitems/listofitems.sty (d:/texlive/2024/texmf-dist/tex/generic/listofitems/listofitems.tex
\loi_cnt_foreach_nest=\count407
\loi_nestcnt=\count408
)
Package: listofitems 2024/03/09 v1.65 Grab items in lists using user-specified sep char (CT)
) (d:/texlive/2024/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count409
\calc@Bcount=\count410
\calc@Adimen=\dimen377
\calc@Bdimen=\dimen378
\calc@Askip=\skip76
\calc@Bskip=\skip77
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count411
\calc@Cskip=\skip78
)
\c@@stackindex=\count412
\@boxshift=\skip79
\stack@tmplength=\skip80
\temp@stkl=\skip81
\@stackedboxwidth=\skip82
\@addedbox=\box130
\@anchorbox=\box131
\@insetbox=\box132
\se@backgroundbox=\box133
\stackedbox=\box134
\@centerbox=\box135
\c@ROWcellindex@=\count413
) (d:/texlive/2024/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip83
\multirow@cntb=\count414
\multirow@dima=\skip84
\bigstrutjot=\dimen379
) (d:/texlive/2024/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen380
\ar@mcellbox=\box136
\extrarowheight=\dimen381
\NC@list=\toks51
\extratabsurround=\skip85
\backup@length=\skip86
\ar@cellbox=\box137
) (d:/texlive/2024/texmf-dist/tex/latex/makecell/makecell.sty
Package: makecell 2009/08/03 V0.1e Managing of Tab Column Heads and Cells
\rotheadsize=\dimen382
\c@nlinenum=\count415
\TeXr@lab=\toks52
) (d:/texlive/2024/texmf-dist/tex/latex/markdown/markdown.sty (d:/texlive/2024/texmf-dist/tex/generic/markdown/markdown.tex (d:/texlive/2024/texmf-dist/tex/generic/lt3luabridge/lt3luabridge.tex
\g_luabridge_method_int=\count416

Package luabridge Info: Using shell escape as the bridging method

)
\markdownInputFileStream=\read3
\markdownOutputFileStream=\write5
)
\markdownOptionFrozenCacheCounter=\count417
Package: markdown 2024-03-09 v3.4.2-0-ga45cf0ed markdown renderer
)

Package markdown Info: Loading LaTeX Markdown theme witiko/markdown/defaults

 (d:/texlive/2024/texmf-dist/tex/latex/markdown/markdownthemewitiko_markdown_defaults.sty
Package: markdownthemewitiko_markdown_defaults 2024/01/03

Package markdown Info: Loading plain TeX Markdown theme
(markdown)             witiko/markdown/defaults

 (d:/texlive/2024/texmf-dist/tex/generic/markdown/markdownthemewitiko_markdown_defaults.tex) (d:/texlive/2024/texmf-dist/tex/latex/csvsimple/csvsimple.sty
Package: csvsimple 2024/01/19 v2.6.0 LaTeX CSV file processing
) (d:/texlive/2024/texmf-dist/tex/latex/csvsimple/csvsimple-legacy.sty
Package: csvsimple-legacy 2024/01/19 version 2.6.0 LaTeX2e CSV file processing
 (d:/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
) (d:/texlive/2024/texmf-dist/tex/latex/tools/shellesc.sty
Package: shellesc 2023/07/08 v1.0d unified shell escape interface for LaTeX
Package shellesc Info: Restricted shell escape enabled on input line 77.
)
\csv@file=\read4
\c@csvinputline=\count418
\c@csvrow=\count419
\c@csvcol=\count420
\csv@out=\write6
) (d:/texlive/2024/texmf-dist/tex/latex/fancyvrb/fancyvrb.sty
Package: fancyvrb 2024/01/20 4.5c verbatim text (tvz,hv)
\FV@CodeLineNo=\count421
\FV@InFile=\read5
\FV@TabBox=\box138
\c@FancyVerbLine=\count422
\FV@StepNumber=\count423
\FV@OutFile=\write7
)
\markdownLaTeXCitationsCounter=\count424
 (d:/texlive/2024/texmf-dist/tex/generic/gobble/gobble.sty
Package: gobble 2019/01/04 v0.2 Provides more gobble macros
 (d:/texlive/2024/texmf-dist/tex/generic/gobble/gobble.tex))
\markdownLaTeXRowCounter=\count425
\markdownLaTeXRowTotal=\count426
\markdownLaTeXColumnCounter=\count427
\markdownLaTeXColumnTotal=\count428
\markdownLaTeXTable=\toks53
\markdownLaTeXTableAlignment=\toks54
\markdownLaTeXTableEnd=\toks55
)

Package fontspec Info: Could not resolve font "Microsoft YaHei Bold/I" (it
(fontspec)             probably doesn't exist).


Package fontspec Info: Could not resolve font "Microsoft YaHei/I" (it probably
(fontspec)             doesn't exist).


Package fontspec Info: Font family 'MicrosoftYaHei(0)' created for font
(fontspec)             'Microsoft YaHei' with options
(fontspec)             [Script={CJK},BoldFont={* Bold}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"Microsoft
(fontspec)             YaHei/OT:script=hani;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->"Microsoft YaHei
(fontspec)             Bold/OT:script=hani;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"Microsoft
(fontspec)             YaHei/BI/OT:script=hani;language=dflt;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.: 


No file slide.aux.
\openout1 = `slide.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 203.
LaTeX Font Info:    ... okay on input line 203.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 203.
LaTeX Font Info:    ... okay on input line 203.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 203.
LaTeX Font Info:    ... okay on input line 203.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 203.
LaTeX Font Info:    ... okay on input line 203.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 203.
LaTeX Font Info:    ... okay on input line 203.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 203.
LaTeX Font Info:    ... okay on input line 203.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 203.
LaTeX Font Info:    ... okay on input line 203.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 203.
LaTeX Font Info:    ... okay on input line 203.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 203.
LaTeX Font Info:    ... okay on input line 203.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 203.
LaTeX Font Info:    ... okay on input line 203.
*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: custom
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: includehead includefoot 
* h-part:(L,W,R)=(28.45274pt, 307.28987pt, 28.45274pt)
* v-part:(T,H,B)=(0.0pt, 273.14662pt, 0.0pt)
* \paperwidth=364.19536pt
* \paperheight=273.14662pt
* \textwidth=307.28987pt
* \textheight=244.6939pt
* \oddsidemargin=-43.81725pt
* \evensidemargin=-43.81725pt
* \topmargin=-72.26999pt
* \headheight=14.22636pt
* \headsep=0.0pt
* \topskip=11.0pt
* \footskip=14.22636pt
* \marginparwidth=4.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 203.
\@outlinefile=\write8
\openout8 = `slide.out'.


Package hyperref Warning: Rerun to get /PageLabels entry.

LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/cmss/m/n on input line 203.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/cmss/b/n on input line 203.
\symnumbers=\mathgroup7
\sympureletters=\mathgroup8
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/cmss/m/n --> T1/lmr/m/n on input line 203.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> T1/lmss/b/n on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> T1/lmss/b/n on input line 203.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> T1/lmss/m/n on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> T1/lmss/m/n on input line 203.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> T1/lmss/m/it on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> T1/lmss/m/it on input line 203.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> T1/lmtt/m/n on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> T1/lmtt/m/n on input line 203.
LaTeX Font Info:    Overwriting symbol font `numbers' in version `bold'
(Font)                  T1/lmss/m/n --> T1/lmss/b/n on input line 203.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `bold'
(Font)                  T1/lmss/m/it --> T1/lmss/b/it on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `bold'
(Font)                  OT1/cmss/b/n --> T1/lmr/b/n on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  T1/lmss/b/n --> T1/lmss/b/n on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  T1/lmss/m/n --> T1/lmss/b/n on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  T1/lmss/m/it --> T1/lmss/b/it on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  T1/lmtt/m/n --> T1/lmtt/b/n on input line 203.
(d:/texlive/2024/texmf-dist/tex/latex/translator/translator-basic-dictionary-English.dict
Dictionary: translator-basic-dictionary, Language: English 
) (d:/texlive/2024/texmf-dist/tex/latex/translator/translator-bibliography-dictionary-English.dict
Dictionary: translator-bibliography-dictionary, Language: English 
) (d:/texlive/2024/texmf-dist/tex/latex/translator/translator-environment-dictionary-English.dict
Dictionary: translator-environment-dictionary, Language: English 
) (d:/texlive/2024/texmf-dist/tex/latex/translator/translator-months-dictionary-English.dict
Dictionary: translator-months-dictionary, Language: English 
) (d:/texlive/2024/texmf-dist/tex/latex/translator/translator-numbers-dictionary-English.dict
Dictionary: translator-numbers-dictionary, Language: English 
) (d:/texlive/2024/texmf-dist/tex/latex/translator/translator-theorem-dictionary-English.dict
Dictionary: translator-theorem-dictionary, Language: English 
) (d:/texlive/2024/texmf-dist/tex/latex/ctex/dictionary/translator-theorem-dictionary-ChineseUTF8.dict
Dictionary: translator-theorem-dictionary, Language: ChineseUTF8 2022/07/14 v2.5.10 Chinese translation for theorem name (CTEX)
)

Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup9
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 203.
LaTeX Font Info:    Redeclaring math accent \acute on input line 203.
LaTeX Font Info:    Redeclaring math accent \grave on input line 203.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 203.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 203.
LaTeX Font Info:    Redeclaring math accent \bar on input line 203.
LaTeX Font Info:    Redeclaring math accent \breve on input line 203.
LaTeX Font Info:    Redeclaring math accent \check on input line 203.
LaTeX Font Info:    Redeclaring math accent \hat on input line 203.
LaTeX Font Info:    Redeclaring math accent \dot on input line 203.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 203.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 203.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 203.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 203.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 203.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 203.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 203.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 203.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 203.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 203.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 203.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 203.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 203.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 203.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 203.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmr/m/n on input line 203.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 203.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmss/b/n --> TU/lmr/m/n on input line 203.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 203.
LaTeX Font Info:    Redeclaring math alphabet \mathrm on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  T1/lmss/m/it --> TU/lmr/m/it on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  T1/lmss/b/n --> TU/lmr/b/n on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  T1/lmss/m/n --> TU/lmss/m/n on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  T1/lmtt/m/n --> TU/lmtt/m/n on input line 203.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  T1/lmss/b/it --> TU/lmr/b/it on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  T1/lmss/b/n --> TU/lmss/b/n on input line 203.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  T1/lmtt/b/n --> TU/lmtt/b/n on input line 203.

No file slide.nav.
[1

]
No file slide.toc.
[2

]
LaTeX Font Info:    Trying to load font information for U+msa on input line 237.
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 237.
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Font shape `T1/lmss/m/it' in size <10.95> not available
(Font)              Font shape `T1/lmss/m/sl' tried instead on input line 237.
LaTeX Font Info:    Font shape `T1/lmss/m/it' in size <8> not available
(Font)              Font shape `T1/lmss/m/sl' tried instead on input line 237.
LaTeX Font Info:    Font shape `T1/lmss/m/it' in size <6> not available
(Font)              Font shape `T1/lmss/m/sl' tried instead on input line 237.
 [3

] [4

]
LaTeX Font Info:    Font shape `T1/lmss/m/it' in size <10> not available
(Font)              Font shape `T1/lmss/m/sl' tried instead on input line 264.
LaTeX Font Info:    Font shape `T1/lmss/m/it' in size <7> not available
(Font)              Font shape `T1/lmss/m/sl' tried instead on input line 264.
LaTeX Font Info:    Font shape `T1/lmss/m/it' in size <5> not available
(Font)              Font shape `T1/lmss/m/sl' tried instead on input line 264.
 [5

] [6

] [7

] [8

])
Runaway argument?
{测试方法：提示词协议} 为了确保测试的公平性和可\ETC.
! File ended while scanning use of \frame.
<inserted text> 
                \par 
<*> ...oads/USTC Beamer Theme_from_OVERLEAF/slide"
                                                  
I suspect you have forgotten a `}', causing me
to read past where you wanted me to stop.
I'll try to recover; but if the error is serious,
you'd better type `E' or `X' now and fix your file.

! Emergency stop.
<*> ...oads/USTC Beamer Theme_from_OVERLEAF/slide"
                                                  
*** (job aborted, no legal \end found)

 
Here is how much of TeX's memory you used:
 32185 strings out of 474773
 722821 string characters out of 5761288
 1935842 words of memory out of 5000000
 53662 multiletter control sequences out of 15000+600000
 611827 words of font info for 116 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 128i,14n,123p,488b,601s stack positions out of 10000i,1000n,20000p,200000b,200000s
Output written on slide.pdf (8 pages).
